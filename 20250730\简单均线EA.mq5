//+------------------------------------------------------------------+
//|                                                    简单均线EA.mq5 |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

#include <Trade\Trade.mqh>

//--- 输入参数
input group "=== 均线参数 ==="
input int      EMA5_Period = 5;           // 5日均线周期
input int      EMA20_Period = 20;         // 20日均线周期  
input int      EMA60_Period = 60;         // 60日均线周期

input group "=== 策略开关 ==="
input bool     Use5_20Strategy = true;    // 启用5/20日均线策略
input bool     Use20_60Strategy = true;   // 启用20/60日均线策略

input group "=== 风险管理 ==="
input double   LotSize = 0.1;             // 手数
input int      StopLoss = 100;            // 止损点数
input int      TakeProfit = 200;          // 止盈点数
input int      TrailingStop = 50;         // 移动止损点数
input int      TrailingStep = 10;         // 移动止损步长

input group "=== 时间过滤 ==="
input bool     UseTimeFilter = false;     // 启用时间过滤
input int      StartHour = 9;             // 开始交易时间
input int      EndHour = 17;              // 结束交易时间

input group "=== 其他设置 ==="
input int      MagicNumber = 123456;      // 魔术数字
input string   TradeComment = "简单均线EA"; // 交易备注

//--- 全局变量
CTrade trade;
int ema5_handle, ema20_handle, ema60_handle;
double ema5[], ema20[], ema60[];
datetime lastBarTime = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- 设置交易参数
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(10);
    trade.SetTypeFilling(ORDER_FILLING_FOK);
    
    //--- 创建均线指标句柄
    ema5_handle = iMA(_Symbol, PERIOD_CURRENT, EMA5_Period, 0, MODE_EMA, PRICE_CLOSE);
    ema20_handle = iMA(_Symbol, PERIOD_CURRENT, EMA20_Period, 0, MODE_EMA, PRICE_CLOSE);
    ema60_handle = iMA(_Symbol, PERIOD_CURRENT, EMA60_Period, 0, MODE_EMA, PRICE_CLOSE);
    
    //--- 检查句柄是否创建成功
    if(ema5_handle == INVALID_HANDLE || ema20_handle == INVALID_HANDLE || ema60_handle == INVALID_HANDLE)
    {
        Print("创建均线指标失败");
        return INIT_FAILED;
    }
    
    //--- 设置数组为时间序列
    ArraySetAsSeries(ema5, true);
    ArraySetAsSeries(ema20, true);
    ArraySetAsSeries(ema60, true);
    
    Print("简单均线EA初始化成功");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- 释放指标句柄
    if(ema5_handle != INVALID_HANDLE) IndicatorRelease(ema5_handle);
    if(ema20_handle != INVALID_HANDLE) IndicatorRelease(ema20_handle);
    if(ema60_handle != INVALID_HANDLE) IndicatorRelease(ema60_handle);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- 检查是否有新K线
    if(!IsNewBar()) return;
    
    //--- 时间过滤
    if(UseTimeFilter && !IsTradeTime()) return;
    
    //--- 获取均线数据
    if(!GetIndicatorData()) return;
    
    //--- 检查交易信号
    CheckTradeSignals();
    
    //--- 管理已有持仓
    ManagePositions();
}

//+------------------------------------------------------------------+
//| 检查是否有新K线                                                    |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    if(currentBarTime != lastBarTime)
    {
        lastBarTime = currentBarTime;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| 检查是否在交易时间内                                               |
//+------------------------------------------------------------------+
bool IsTradeTime()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    return (dt.hour >= StartHour && dt.hour < EndHour);
}

//+------------------------------------------------------------------+
//| 获取指标数据                                                      |
//+------------------------------------------------------------------+
bool GetIndicatorData()
{
    //--- 复制均线数据
    if(CopyBuffer(ema5_handle, 0, 0, 3, ema5) < 3) return false;
    if(CopyBuffer(ema20_handle, 0, 0, 3, ema20) < 3) return false;
    if(CopyBuffer(ema60_handle, 0, 0, 3, ema60) < 3) return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| 检查交易信号                                                      |
//+------------------------------------------------------------------+
void CheckTradeSignals()
{
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    //--- 5/20均线策略信号
    bool signal5_20 = false;
    if(Use5_20Strategy)
    {
        // 价格突破5日均线且价格在20日均线之上
        signal5_20 = (currentPrice > ema20[0]) && 
                     (currentPrice > ema5[0]) && 
                     (iClose(_Symbol, PERIOD_CURRENT, 1) <= ema5[1]);
    }
    
    //--- 20/60均线策略信号
    bool signal20_60 = false;
    if(Use20_60Strategy)
    {
        // 价格突破20日均线且价格在60日均线之上
        signal20_60 = (currentPrice > ema60[0]) && 
                      (currentPrice > ema20[0]) && 
                      (iClose(_Symbol, PERIOD_CURRENT, 1) <= ema20[1]);
    }
    
    //--- 执行买入信号
    if((signal5_20 || signal20_60) && PositionsTotal() == 0)
    {
        OpenBuyOrder();
    }
    
    //--- 检查平仓信号
    if(PositionsTotal() > 0)
    {
        // 价格跌破5日均线时平仓
        if(currentPrice < ema5[0] && iClose(_Symbol, PERIOD_CURRENT, 1) >= ema5[1])
        {
            CloseAllPositions();
        }
    }
}

//+------------------------------------------------------------------+
//| 开仓买单                                                          |
//+------------------------------------------------------------------+
void OpenBuyOrder()
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl = 0, tp = 0;
    
    //--- 计算止损止盈
    if(StopLoss > 0)
        sl = ask - StopLoss * _Point;
    if(TakeProfit > 0)
        tp = ask + TakeProfit * _Point;
    
    //--- 开仓
    if(trade.Buy(LotSize, _Symbol, ask, sl, tp, TradeComment))
    {
        Print("买单开仓成功，价格：", ask);
    }
    else
    {
        Print("买单开仓失败，错误代码：", trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| 平仓所有持仓                                                      |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(PositionSelectByTicket(ticket))
        {
            if(PositionGetInteger(POSITION_MAGIC) == MagicNumber)
            {
                trade.PositionClose(ticket);
                Print("平仓持仓，票据：", ticket);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 管理持仓（移动止损）                                               |
//+------------------------------------------------------------------+
void ManagePositions()
{
    if(TrailingStop <= 0) return;
    
    for(int i = 0; i < PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(PositionSelectByTicket(ticket))
        {
            if(PositionGetInteger(POSITION_MAGIC) == MagicNumber)
            {
                double positionProfit = PositionGetDouble(POSITION_PROFIT);
                if(positionProfit > 0)
                {
                    TrailingStopLoss(ticket);
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 移动止损                                                          |
//+------------------------------------------------------------------+
void TrailingStopLoss(ulong ticket)
{
    if(!PositionSelectByTicket(ticket)) return;
    
    double currentSL = PositionGetDouble(POSITION_SL);
    double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    double newSL = currentPrice - TrailingStop * _Point;
    
    //--- 只有当新止损更有利时才修改
    if(newSL > currentSL + TrailingStep * _Point && newSL > openPrice)
    {
        double tp = PositionGetDouble(POSITION_TP);
        if(trade.PositionModify(ticket, newSL, tp))
        {
            Print("移动止损成功，新止损：", newSL);
        }
    }
}
