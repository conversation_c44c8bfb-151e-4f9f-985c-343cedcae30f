# 简单均线EA使用说明

## 概述
本EA基于Pine Script的"多组均线突破策略（5/20/60）"转换而来，实现了完整的MQ5自动交易系统，包含完善的风险管理、进出场规则和止盈止损功能。

## 文件说明
- `简单均线EA.mq5` - 基础版EA
- `简单均线EA增强版.mq5` - 增强版EA（推荐使用）
- `简单均线EA配置.mqh` - 配置文件和辅助类
- `简单均线EA使用说明.md` - 本说明文档

## 交易策略

### 核心逻辑
1. **5/20均线策略**：价格突破5日均线且价格在20日均线之上时买入
2. **20/60均线策略**：价格突破20日均线且价格在60日均线之上时买入
3. **统一出场**：价格跌破5日均线时平仓

### 进场条件
- **5/20策略**：`价格 > 20日均线 AND 价格突破5日均线`
- **20/60策略**：`价格 > 60日均线 AND 价格突破20日均线`
- 两个策略可以独立开启/关闭

### 出场条件
- 价格跌破5日均线
- 达到止损/止盈点位
- 移动止损触发

## 参数设置

### 均线参数
- `EMA5_Period` (5) - 5日均线周期
- `EMA20_Period` (20) - 20日均线周期
- `EMA60_Period` (60) - 60日均线周期

### 策略开关
- `Use5_20Strategy` (true) - 启用5/20日均线策略
- `Use20_60Strategy` (true) - 启用20/60日均线策略
- `UseSignalFilter` (true) - 启用信号过滤（仅增强版）

### 风险管理
- `UseFixedLot` (true) - 使用固定手数
- `FixedLotSize` (0.1) - 固定手数大小
- `RiskPercent` (2.0) - 风险百分比（动态手数时使用）
- `MaxDailyLoss` (1000.0) - 最大日损失限制
- `StopLoss` (100) - 止损点数
- `TakeProfit` (200) - 止盈点数

### 移动止损
- `UseTrailingStop` (true) - 启用移动止损
- `TrailingStop` (50) - 移动止损距离
- `TrailingStep` (10) - 移动止损步长
- `TrailingStart` (50) - 移动止损启动点数

### 时间过滤
- `UseTimeFilter` (false) - 启用时间过滤
- `StartHour` (9) - 开始交易时间
- `EndHour` (17) - 结束交易时间
- `AvoidNews` (true) - 避开新闻时间（仅增强版）

## 安装步骤

1. **复制文件**
   - 将所有.mq5和.mqh文件复制到MT5的`MQL5\Experts`目录
   - 重新编译或重启MT5

2. **添加到图表**
   - 在MT5中打开要交易的品种图表
   - 将EA拖拽到图表上
   - 设置参数并启用自动交易

3. **参数配置**
   - 根据交易品种特性调整均线周期
   - 设置合适的止损止盈点数
   - 配置风险管理参数

## 风险管理特性（增强版）

### 资金管理
- **固定手数模式**：使用固定的交易手数
- **风险百分比模式**：根据账户余额和风险百分比动态计算手数
- **日损失限制**：达到最大日损失后停止交易

### 信号过滤
- **时间间隔过滤**：避免频繁交易
- **波动性过滤**：使用ATR指标过滤低波动市场
- **趋势强度过滤**：使用ADX指标确保趋势足够强劲

### 交易统计
- 实时统计交易胜率、盈亏比等关键指标
- 定期显示交易统计信息
- 帮助优化交易策略

## 使用建议

### 适用市场
- **趋势性较强的市场**：如主要货币对、股指等
- **流动性好的品种**：避免滑点过大
- **波动性适中的时段**：避免过度波动或过于平静的市场

### 参数优化
1. **回测优化**：使用历史数据测试不同参数组合
2. **品种适配**：不同品种可能需要不同的参数设置
3. **时间周期**：可以在不同时间框架上测试效果

### 风险控制
- 建议使用增强版EA，具有更完善的风险管理
- 设置合理的止损止盈比例（如1:2）
- 控制单笔交易风险不超过账户的2-3%
- 定期检查交易统计，及时调整策略

## 注意事项

1. **市场风险**：任何自动交易系统都存在亏损风险
2. **参数调整**：需要根据市场变化适时调整参数
3. **监控重要**：虽然是自动交易，但仍需定期监控
4. **新闻影响**：重大新闻发布时建议暂停EA
5. **网络稳定**：确保网络连接稳定，避免错失交易机会

## 技术支持

如遇到问题，请检查：
1. MT5版本是否支持
2. 编译是否成功
3. 参数设置是否合理
4. 账户权限是否允许自动交易

## 版本历史

- **v1.0** - 基础版本，实现核心交易逻辑
- **v2.0** - 增强版本，添加风险管理和信号过滤功能

---

**免责声明**：本EA仅供学习和研究使用，实盘交易存在风险，请谨慎使用并做好风险控制。
