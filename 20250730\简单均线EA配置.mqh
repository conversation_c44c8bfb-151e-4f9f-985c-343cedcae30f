//+------------------------------------------------------------------+
//|                                              简单均线EA配置.mqh |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"

//+------------------------------------------------------------------+
//| 风险管理类                                                        |
//+------------------------------------------------------------------+
class CRiskManager
{
private:
    double m_maxRiskPercent;        // 最大风险百分比
    double m_maxDailyLoss;          // 最大日损失
    double m_dailyProfit;           // 当日盈利
    double m_dailyLoss;             // 当日亏损
    datetime m_lastResetDate;       // 上次重置日期

public:
    CRiskManager(double maxRisk = 2.0, double maxDailyLoss = 1000.0)
    {
        m_maxRiskPercent = maxRisk;
        m_maxDailyLoss = maxDailyLoss;
        m_dailyProfit = 0;
        m_dailyLoss = 0;
        m_lastResetDate = 0;
    }
    
    //--- 检查是否可以开仓
    bool CanOpenPosition()
    {
        ResetDailyCounters();
        
        // 检查日损失限制
        if(m_dailyLoss >= m_maxDailyLoss)
        {
            Print("达到日损失限制，停止交易");
            return false;
        }
        
        // 检查账户余额
        double balance = AccountInfoDouble(ACCOUNT_BALANCE);
        if(balance <= 0)
        {
            Print("账户余额不足");
            return false;
        }
        
        return true;
    }
    
    //--- 计算合适的手数
    double CalculateLotSize(double stopLoss, double riskPercent = 0)
    {
        if(riskPercent <= 0) riskPercent = m_maxRiskPercent;
        
        double balance = AccountInfoDouble(ACCOUNT_BALANCE);
        double riskAmount = balance * riskPercent / 100.0;
        
        double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
        double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
        double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
        double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
        double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
        
        if(stopLoss <= 0 || tickValue <= 0 || tickSize <= 0)
            return minLot;
        
        double lotSize = riskAmount / (stopLoss * tickValue / tickSize);
        
        // 规范化手数
        lotSize = MathFloor(lotSize / lotStep) * lotStep;
        lotSize = MathMax(lotSize, minLot);
        lotSize = MathMin(lotSize, maxLot);
        
        return lotSize;
    }
    
    //--- 更新日盈亏
    void UpdateDailyPnL()
    {
        ResetDailyCounters();
        
        double todayProfit = 0;
        double todayLoss = 0;
        
        // 计算今日盈亏
        datetime today = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
        
        if(HistorySelect(today, TimeCurrent()))
        {
            int total = HistoryDealsTotal();
            for(int i = 0; i < total; i++)
            {
                ulong ticket = HistoryDealGetTicket(i);
                if(HistoryDealSelect(ticket))
                {
                    double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                    if(profit > 0)
                        todayProfit += profit;
                    else
                        todayLoss += MathAbs(profit);
                }
            }
        }
        
        m_dailyProfit = todayProfit;
        m_dailyLoss = todayLoss;
    }
    
private:
    //--- 重置日计数器
    void ResetDailyCounters()
    {
        datetime currentDate = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
        if(m_lastResetDate != currentDate)
        {
            m_dailyProfit = 0;
            m_dailyLoss = 0;
            m_lastResetDate = currentDate;
        }
    }
};

//+------------------------------------------------------------------+
//| 信号过滤器类                                                      |
//+------------------------------------------------------------------+
class CSignalFilter
{
private:
    int m_minBarsFromLastTrade;     // 距离上次交易的最小K线数
    datetime m_lastTradeTime;       // 上次交易时间
    
public:
    CSignalFilter(int minBars = 5)
    {
        m_minBarsFromLastTrade = minBars;
        m_lastTradeTime = 0;
    }
    
    //--- 检查信号是否有效
    bool IsSignalValid()
    {
        // 检查距离上次交易的时间间隔
        if(m_lastTradeTime > 0)
        {
            int barsSinceLastTrade = Bars(_Symbol, PERIOD_CURRENT, m_lastTradeTime, TimeCurrent());
            if(barsSinceLastTrade < m_minBarsFromLastTrade)
            {
                return false;
            }
        }
        
        // 检查市场波动性
        if(!CheckVolatility())
            return false;
            
        // 检查趋势强度
        if(!CheckTrendStrength())
            return false;
        
        return true;
    }
    
    //--- 更新最后交易时间
    void UpdateLastTradeTime()
    {
        m_lastTradeTime = TimeCurrent();
    }
    
private:
    //--- 检查市场波动性
    bool CheckVolatility()
    {
        double atr[];
        int atr_handle = iATR(_Symbol, PERIOD_CURRENT, 14);
        if(atr_handle == INVALID_HANDLE) return true;
        
        ArraySetAsSeries(atr, true);
        if(CopyBuffer(atr_handle, 0, 0, 1, atr) < 1)
        {
            IndicatorRelease(atr_handle);
            return true;
        }
        
        double currentATR = atr[0];
        double minATR = 10 * _Point; // 最小波动性要求
        
        IndicatorRelease(atr_handle);
        return (currentATR >= minATR);
    }
    
    //--- 检查趋势强度
    bool CheckTrendStrength()
    {
        double adx[];
        int adx_handle = iADX(_Symbol, PERIOD_CURRENT, 14);
        if(adx_handle == INVALID_HANDLE) return true;
        
        ArraySetAsSeries(adx, true);
        if(CopyBuffer(adx_handle, 0, 0, 1, adx) < 1)
        {
            IndicatorRelease(adx_handle);
            return true;
        }
        
        double currentADX = adx[0];
        double minADX = 25.0; // 最小趋势强度要求
        
        IndicatorRelease(adx_handle);
        return (currentADX >= minADX);
    }
};

//+------------------------------------------------------------------+
//| 交易统计类                                                        |
//+------------------------------------------------------------------+
class CTradeStatistics
{
private:
    int m_totalTrades;              // 总交易数
    int m_winTrades;                // 盈利交易数
    int m_lossTrades;               // 亏损交易数
    double m_totalProfit;           // 总盈利
    double m_totalLoss;             // 总亏损
    double m_maxProfit;             // 最大盈利
    double m_maxLoss;               // 最大亏损
    
public:
    CTradeStatistics()
    {
        Reset();
    }
    
    //--- 重置统计
    void Reset()
    {
        m_totalTrades = 0;
        m_winTrades = 0;
        m_lossTrades = 0;
        m_totalProfit = 0;
        m_totalLoss = 0;
        m_maxProfit = 0;
        m_maxLoss = 0;
    }
    
    //--- 添加交易结果
    void AddTrade(double profit)
    {
        m_totalTrades++;
        
        if(profit > 0)
        {
            m_winTrades++;
            m_totalProfit += profit;
            if(profit > m_maxProfit)
                m_maxProfit = profit;
        }
        else
        {
            m_lossTrades++;
            m_totalLoss += MathAbs(profit);
            if(MathAbs(profit) > m_maxLoss)
                m_maxLoss = MathAbs(profit);
        }
    }
    
    //--- 获取胜率
    double GetWinRate()
    {
        if(m_totalTrades == 0) return 0;
        return (double)m_winTrades / m_totalTrades * 100.0;
    }
    
    //--- 获取盈亏比
    double GetProfitFactor()
    {
        if(m_totalLoss == 0) return 0;
        return m_totalProfit / m_totalLoss;
    }
    
    //--- 打印统计信息
    void PrintStatistics()
    {
        Print("=== 交易统计 ===");
        Print("总交易数: ", m_totalTrades);
        Print("盈利交易: ", m_winTrades);
        Print("亏损交易: ", m_lossTrades);
        Print("胜率: ", DoubleToString(GetWinRate(), 2), "%");
        Print("盈亏比: ", DoubleToString(GetProfitFactor(), 2));
        Print("总盈利: ", DoubleToString(m_totalProfit, 2));
        Print("总亏损: ", DoubleToString(m_totalLoss, 2));
        Print("最大盈利: ", DoubleToString(m_maxProfit, 2));
        Print("最大亏损: ", DoubleToString(m_maxLoss, 2));
        Print("净盈利: ", DoubleToString(m_totalProfit - m_totalLoss, 2));
    }
};
